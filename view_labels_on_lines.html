<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Process Analysis v13 - Labels on Lines</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 4px solid #667eea;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            color: #6c757d;
            margin: 10px 0 0 0;
            font-size: 1.2em;
        }
        
        .labels-badge {
            display: inline-block;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #dee2e6;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
        }
        
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
        }
        
        .controls {
            text-align: center;
            margin: 25px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 10px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .info-card ul {
            color: #495057;
            line-height: 1.6;
        }
        
        .zoom-controls {
            margin: 15px 0;
        }
        
        .zoom-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .improvement {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .improvement h4 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        
        .improvement p {
            color: #155724;
            margin: 5px 0;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sensor Process Analysis v13</h1>
            <p>Overleaf-Optimized with Perfect Visibility</p>
            <div class="labels-badge">📄 OVERLEAF READY</div>
        </div>
        
        <div class="improvement">
            <h4>🎯 Overleaf-Optimized Features</h4>
            <p><strong>Layout:</strong> Horizontal expansion for better visibility | <strong>Labels:</strong> Exactly on curve paths</p>
            <p><strong>Circles:</strong> Perfect start/end nodes | <strong>Resolution:</strong> 400 DPI publication quality</p>
        </div>

        <div class="info-grid" style="margin-top: 20px; margin-bottom: 20px;">
            <div class="info-card">
                <h3>➡️ Flow Directions & Values</h3>
                <ul>
                    <li><strong>Start → Stations:</strong> 4,755 → S202 | 5,592 → S203 | 4,474 → S204</li>
                    <li><strong>Green Flows:</strong> S206→S207 (8,276) | S208→S207 (7,596) | S208→S209 (2,529)</li>
                    <li><strong>To End:</strong> S208→End (6,028) | S209→End (1,476) | S207→End (6,379)</li>
                    <li><strong>Red Bottlenecks:</strong> S205→S204 (8,485) | S202→S203 (8,947) | S203→S204 (9,200)</li>
                    <li><strong>Internal Flows:</strong> S202→S206 (529) | S205→S206 (2,888) | S207→S209 (576)</li>
                </ul>
            </div>
        </div>
        
        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                <button class="zoom-btn" onclick="fullscreen()">⛶ Fullscreen</button>
            </div>
            
            <img id="diagram"
                 src="sensor_process_analysis_v13_overleaf_optimized.png"
                 alt="Overleaf-Optimized Sensor Process Analysis v13"
                 class="diagram-image"
                 onclick="toggleFullscreen()">
        </div>
        
        <div class="controls">
            <a href="sensor_process_analysis_v13_overleaf_optimized.png" download class="btn">📥 Download Overleaf PNG</a>
            <a href="sensor_process_analysis_v13_overleaf_optimized.pdf" download class="btn">📄 Download Overleaf PDF</a>
            <a href="sensor_process_analysis_v13_overleaf_optimized.svg" download class="btn">🎨 Download Overleaf SVG</a>
            <a href="network_diagram_v13.py" download class="btn">💻 Download Code</a>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🎯 Overleaf Optimizations</h3>
                <ul>
                    <li><strong>Horizontal layout:</strong> 18×8 format perfect for papers</li>
                    <li><strong>Perfect circles:</strong> Start/end nodes are true circles, not ovals</li>
                    <li><strong>Curve-perfect labels:</strong> 6,028 and 1,476 exactly on curve paths</li>
                    <li><strong>Enhanced spacing:</strong> Expanded horizontally for clarity</li>
                    <li><strong>Professional colors:</strong> Publication-ready color scheme</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🔧 Technical Improvements</h3>
                <ul>
                    <li><strong>Smart positioning:</strong> Different logic for straight vs curved lines</li>
                    <li><strong>Curve detection:</strong> Automatic detection of significant curves</li>
                    <li><strong>Minimal displacement:</strong> Labels stay as close as possible to lines</li>
                    <li><strong>Better contrast:</strong> White background with colored borders</li>
                    <li><strong>Optimal sizing:</strong> Font size 11 for perfect readability</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>📊 Visual Clarity</h3>
                <ul>
                    <li><strong>Clear flow identification:</strong> No confusion about which value belongs where</li>
                    <li><strong>Professional appearance:</strong> Clean, publication-ready styling</li>
                    <li><strong>High resolution:</strong> 400 DPI for crystal clear text</li>
                    <li><strong>MDPI compliant:</strong> Correct thousands separator formatting</li>
                    <li><strong>Color coordination:</strong> Label borders match line colors</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        const diagram = document.getElementById('diagram');
        
        function zoomIn() {
            currentZoom *= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            diagram.style.transform = 'scale(1)';
        }
        
        function fullscreen() {
            if (diagram.requestFullscreen) {
                diagram.requestFullscreen();
            } else if (diagram.webkitRequestFullscreen) {
                diagram.webkitRequestFullscreen();
            } else if (diagram.msRequestFullscreen) {
                diagram.msRequestFullscreen();
            }
        }
        
        function toggleFullscreen() {
            fullscreen();
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                zoomIn();
            } else if (e.key === '-') {
                e.preventDefault();
                zoomOut();
            } else if (e.key === '0') {
                e.preventDefault();
                resetZoom();
            } else if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                fullscreen();
            }
        });
        
        // Mouse wheel zoom
        diagram.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    </script>
</body>
</html>
