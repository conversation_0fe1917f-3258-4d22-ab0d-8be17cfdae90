"""
Test script to verify all dependencies are installed correctly
"""

def test_imports():
    """Test that all required packages can be imported"""
    try:
        import networkx as nx
        print("✓ NetworkX imported successfully")
        
        import matplotlib.pyplot as plt
        print("✓ Matplotlib imported successfully")
        
        import numpy as np
        print("✓ NumPy imported successfully")
        
        import pandas as pd
        print("✓ Pandas imported successfully")
        
        # Test optional packages
        try:
            import plotly
            print("✓ Plotly imported successfully")
        except ImportError:
            print("⚠ Plotly not available (optional)")
            
        try:
            import seaborn
            print("✓ Seaborn imported successfully")
        except ImportError:
            print("⚠ Seaborn not available (optional)")
            
        try:
            from PIL import Image
            print("✓ Pillow imported successfully")
        except ImportError:
            print("⚠ Pillow not available (optional)")
            
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_basic_functionality():
    """Test basic NetworkX and Matplotlib functionality"""
    try:
        import networkx as nx
        import matplotlib.pyplot as plt
        
        # Create a simple graph
        G = nx.Graph()
        G.add_edge('A', 'B')
        G.add_edge('B', 'C')
        
        # Test that we can create a figure
        fig, ax = plt.subplots(figsize=(6, 4))
        pos = nx.spring_layout(G)
        nx.draw(G, pos, ax=ax, with_labels=True)
        
        # Save a test image
        fig.savefig('test_graph.png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print("✓ Basic NetworkX and Matplotlib functionality working")
        print("✓ Test graph saved as 'test_graph.png'")
        return True
        
    except Exception as e:
        print(f"✗ Functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Process Discovery Network Diagram Dependencies")
    print("=" * 55)
    
    # Test imports
    imports_ok = test_imports()
    print()
    
    # Test basic functionality
    if imports_ok:
        functionality_ok = test_basic_functionality()
        print()
        
        if functionality_ok:
            print("🎉 All tests passed! Your installation is ready.")
            print("You can now run: python network_diagram.py")
        else:
            print("❌ Some functionality tests failed.")
    else:
        print("❌ Import tests failed. Please check your installation.")
        print("Try running: pip install -r requirements.txt")
