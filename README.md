# Process Discovery Network Diagram Generator

This Python project creates network diagrams for process discovery analysis, similar to sensor process analysis diagrams with stations, flows, and bottleneck identification.

## Features

- Create network diagrams with stations and connections
- Identify and highlight bottlenecks in red
- Show flow counts on connections
- Customizable station positions and styling
- Export diagrams as images

## Installation

### Option 1: Using pip (recommended)
```bash
pip install -r requirements.txt
```

### Option 2: Using the setup script
```bash
python setup.py install
```

### Option 3: Manual installation of core dependencies
```bash
pip install networkx matplotlib numpy pandas
```

## Quick Start

1. **Run the sample diagram:**
```bash
python network_diagram.py
```

2. **Create your own diagram:**
```python
from network_diagram import ProcessNetworkDiagram

# Create a new diagram
diagram = ProcessNetworkDiagram()

# Add stations
diagram.add_station('Station 1', (0, 0), 1000, is_bottleneck=False)
diagram.add_station('Station 2', (3, 0), 800, is_bottleneck=True)

# Add connections
diagram.add_connection('Station 1', 'Station 2', 500, is_bottleneck_path=True)

# Draw and display
fig = diagram.draw_diagram("My Process Analysis")
plt.show()
```

## Dependencies

### Core Dependencies
- **networkx**: For creating and managing network graphs
- **matplotlib**: For plotting and visualization
- **numpy**: For numerical operations
- **pandas**: For data handling

### Optional Dependencies
- **graphviz/pygraphviz**: For advanced graph layouts
- **plotly/bokeh**: For interactive diagrams
- **Pillow**: For image processing
- **seaborn**: For enhanced styling

## Usage Examples

### Basic Network Diagram
The main script (`network_diagram.py`) includes a sample that recreates the diagram from your image with:
- Process Start and End nodes
- Multiple stations (202-209)
- Flow connections with counts
- Bottleneck identification
- Color coding (red for bottlenecks, green for normal flow)

### Customization Options
- **Node colors**: Modify `node_colors` dictionary
- **Edge colors**: Modify `edge_colors` dictionary  
- **Positions**: Adjust coordinates in `add_station()` calls
- **Styling**: Customize fonts, sizes, and shapes in `draw_diagram()`

## File Structure
```
Process Discovery/
├── requirements.txt      # Python dependencies
├── setup.py             # Installation script
├── network_diagram.py   # Main diagram generator
└── README.md           # This file
```

## Troubleshooting

### Common Issues
1. **Import errors**: Make sure all dependencies are installed
2. **Display issues**: Ensure you have a GUI backend for matplotlib
3. **Graphviz errors**: Install graphviz system package if using advanced layouts

### System Requirements
- Python 3.8 or higher
- GUI backend for matplotlib (usually included)
- Optional: Graphviz system package for advanced layouts

## Contributing

Feel free to modify the code to match your specific process discovery needs. The diagram structure is flexible and can be adapted for different types of process flows.
