"""
Network Diagram Generator for Process Discovery
Creates a network diagram similar to the sensor process analysis shown in the image
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
def create_sensor_process_diagram():
    """Create a professional sensor process diagram with curved lines matching the uploaded image"""

    # Create figure with white background and professional styling
    fig, ax = plt.subplots(figsize=(16, 10), dpi=300, facecolor='white')
    ax.set_facecolor('#F8F9FA')  # Very light gray background for professional look

    # Node positions to match the image layout exactly
    positions = {
        'Process Start': (1.5, 5.5),
        'Station 202': (3.5, 6.5),
        'Station 203': (6.5, 6.5),
        'Station 204': (9.5, 6.5),
        'Station 205': (3.5, 4.0),
        'Station 206': (6.5, 4.0),
        'Station 207': (9.5, 4.0),
        'Station 208': (3.5, 1.5),
        'Station 209': (6.5, 1.5),
        'Process End': (12, 2.5)
    }

    # Node properties (events and bottleneck status from image)
    node_data = {
        'Process Start': {'events': None, 'bottleneck': False, 'type': 'start'},
        'Station 202': {'events': 47559, 'bottleneck': True, 'type': 'station'},
        'Station 203': {'events': 55921, 'bottleneck': True, 'type': 'station'},
        'Station 204': {'events': 44746, 'bottleneck': True, 'type': 'station'},
        'Station 205': {'events': 52673, 'bottleneck': True, 'type': 'station'},
        'Station 206': {'events': 51387, 'bottleneck': False, 'type': 'station'},
        'Station 207': {'events': 63798, 'bottleneck': False, 'type': 'station'},
        'Station 208': {'events': 60281, 'bottleneck': False, 'type': 'station'},
        'Station 209': {'events': 14762, 'bottleneck': False, 'type': 'station'},
        'Process End': {'events': None, 'bottleneck': False, 'type': 'end'}
    }

    # Edge connections with weights, colors, and curve specifications (v6 update)
    edges = [
        # From Process Start (green arrows with slight curves)
        ('Process Start', 'Station 202', 4755, '#2E8B57', 0.1, True),   # Sea green with label
        ('Process Start', 'Station 203', 5692, '#2E8B57', 0.0, True),   # Straight with label
        ('Process Start', 'Station 204', 4474, '#2E8B57', -0.1, True),  # Slight curve with label

        # Green connections (normal flow) with labels
        ('Station 206', 'Station 207', 8276, '#2E8B57', 0.0, True),     # Straight with label
        ('Station 208', 'Station 207', 7596, '#2E8B57', 0.2, True),     # Curved with label
        ('Station 208', 'Station 209', 2529, '#2E8B57', 0.0, True),     # Straight with label
        ('Station 208', 'Process End', 6028, '#2E8B57', 0.3, True),     # Curved with label
        ('Station 209', 'Process End', 1476, '#2E8B57', 0.1, True),     # Small curve with label
        ('Station 207', 'Process End', 6379, '#2E8B57', 0.4, True),     # Large curve with label

        # Red connections without labels (no-label)
        ('Station 208', 'Station 205', None, '#DC143C', 0.2, False),    # Curved, no label
        ('Station 205', 'Station 202', None, '#DC143C', 0.1, False),    # Curved, no label
        ('Station 202', 'Station 209', None, '#DC143C', 0.4, False),    # Large curve, no label
        ('Station 202', 'Station 207', None, '#DC143C', 0.3, False),    # Curved, no label
        ('Station 206', 'Station 203', None, '#DC143C', 0.2, False),    # Curved, no label
        ('Station 209', 'Station 206', None, '#DC143C', 0.3, False),    # Curved, no label

        # Red connections with labels
        ('Station 205', 'Station 204', 8485, '#DC143C', 0.2, True),     # Curved with label
        ('Station 205', 'Station 203', 529, '#DC143C', 0.3, True),      # Curved with label
        ('Station 202', 'Station 203', 8947, '#DC143C', 0.2, True),     # Curved with label
        ('Station 203', 'Station 204', 9200, '#DC143C', 0.0, True),     # Straight with label
    ]

    # Draw edges first (so they appear behind nodes) with professional curved lines
    for start, end, weight, color, curve, show_label in edges:
        start_pos = positions[start]
        end_pos = positions[end]

        # Create curved arrow with professional styling
        connectionstyle = f"arc3,rad={curve}" if curve != 0 else "arc3,rad=0"

        ax.annotate('', xy=end_pos, xytext=start_pos,
                   arrowprops=dict(
                       arrowstyle='->',
                       color=color,
                       lw=3.0,  # Thicker lines for professional look
                       alpha=0.85,
                       connectionstyle=connectionstyle,
                       shrinkA=25, shrinkB=25  # Don't overlap with nodes
                   ))

        # Add weight label with professional styling (only if show_label is True and weight exists)
        if show_label and weight is not None:
            if curve != 0:
                # For curved lines, adjust label position
                mid_x = (start_pos[0] + end_pos[0]) / 2 + curve * 0.5
                mid_y = (start_pos[1] + end_pos[1]) / 2 + curve * 0.3
            else:
                mid_x = (start_pos[0] + end_pos[0]) / 2
                mid_y = (start_pos[1] + end_pos[1]) / 2

            # Professional label styling with thousands separator (MDPI rule)
            ax.text(mid_x, mid_y, f"{weight:,}", fontsize=10, fontweight='bold',
                   ha='center', va='center', color='#2C3E50',  # Dark blue-gray
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white',
                            alpha=0.95, edgecolor='#BDC3C7', linewidth=1))

    # Draw nodes with professional styling
    for node, pos in positions.items():
        data = node_data[node]
        x, y = pos

        if data['type'] in ['start', 'end']:
            # Professional circular nodes for start/end with gradient effect
            circle = plt.Circle((x, y), 0.45, facecolor='#3498DB', edgecolor='#2980B9', linewidth=3)
            ax.add_patch(circle)

            # Add subtle shadow effect
            shadow = plt.Circle((x + 0.05, y - 0.05), 0.45, facecolor='#BDC3C7', alpha=0.3, zorder=0)
            ax.add_patch(shadow)

            label = 'Process Start' if data['type'] == 'start' else 'Process End'
            ax.text(x, y, label, ha='center', va='center', fontsize=11,
                   fontweight='bold', color='white')
        else:
            # Professional rectangular nodes for stations
            width, height = 2.2, 1.2

            # Enhanced color scheme based on bottleneck status
            if data['bottleneck']:
                facecolor = '#FADBD8'    # Light red background
                edgecolor = '#E74C3C'    # Professional red border
                text_color = '#C0392B'   # Dark red text
                border_width = 3
                shadow_color = '#F1948A'
            else:
                facecolor = '#EBF5FB'    # Light blue background
                edgecolor = '#3498DB'    # Professional blue border
                text_color = '#2E86AB'   # Dark blue text
                border_width = 2
                shadow_color = '#AED6F1'

            # Add shadow for depth
            shadow_rect = plt.Rectangle((x - width/2 + 0.05, y - height/2 - 0.05),
                                      width, height, facecolor=shadow_color,
                                      alpha=0.3, zorder=0)
            ax.add_patch(shadow_rect)

            # Draw main rectangle with rounded corners effect
            rect = plt.Rectangle((x - width/2, y - height/2), width, height,
                               facecolor=facecolor, edgecolor=edgecolor,
                               linewidth=border_width, alpha=0.95, zorder=1)
            ax.add_patch(rect)

            # Add station name with better typography
            ax.text(x, y + 0.25, node, ha='center', va='center',
                   fontsize=12, fontweight='bold', color=text_color)

            # Add event count with professional formatting
            ax.text(x, y - 0.05, f"{data['events']:,} events", ha='center', va='center',
                   fontsize=10, color=text_color, style='italic')

            # Add BOTTLENECK label for bottleneck stations with enhanced styling
            if data['bottleneck']:
                ax.text(x, y - 0.4, "BOTTLENECK", ha='center', va='center',
                       fontsize=9, fontweight='bold', color='#C0392B',
                       bbox=dict(boxstyle="round,pad=0.2", facecolor='#FADBD8',
                                edgecolor='#E74C3C', linewidth=1))

    # Set axis limits and properties for professional layout
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 8)
    ax.set_aspect('equal')
    ax.axis('off')

    # Add professional title with enhanced typography
    ax.text(7, 7.5, 'Sensor Process Analysis',
           ha='center', va='center', fontsize=20, fontweight='bold',
           color='#2C3E50')
    ax.text(7, 7.1, 'Process Discovery',
           ha='center', va='center', fontsize=14, fontweight='normal',
           color='#7F8C8D', style='italic')

    # Add professional border/frame
    frame = plt.Rectangle((0.2, 0.2), 13.6, 7.6, fill=False,
                         edgecolor='#BDC3C7', linewidth=2, alpha=0.5)
    ax.add_patch(frame)

    # Add subtle grid for professional look
    for i in range(1, 14):
        ax.axvline(x=i, color='#ECF0F1', alpha=0.3, linewidth=0.5)
    for i in range(1, 8):
        ax.axhline(y=i, color='#ECF0F1', alpha=0.3, linewidth=0.5)

    plt.tight_layout()
    return fig, ax

if __name__ == "__main__":
    # Create and display the professional diagram (v6)
    fig, ax = create_sensor_process_diagram()

    # Save the diagram with high quality and v6 naming
    output_path = 'sensor_process_analysis_v6.png'
    fig.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white',
               edgecolor='none', pad_inches=0.2)
    print(f"Professional network diagram v6 saved as '{output_path}'")

    # Also save as PDF for vector graphics
    pdf_path = 'sensor_process_analysis_v6.pdf'
    fig.savefig(pdf_path, bbox_inches='tight', facecolor='white',
               edgecolor='none', pad_inches=0.2)
    print(f"Professional vector diagram v6 saved as '{pdf_path}'")

    # Save as SVG for web use
    svg_path = 'sensor_process_analysis_v6.svg'
    fig.savefig(svg_path, bbox_inches='tight', facecolor='white',
               edgecolor='none', pad_inches=0.2)
    print(f"Professional SVG diagram v6 saved as '{svg_path}'")

    # Also show the diagram (comment out if running headless)
    plt.show()
