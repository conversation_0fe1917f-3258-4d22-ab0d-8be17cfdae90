"""
Network Diagram Generator for Process Discovery
Creates a network diagram similar to the sensor process analysis shown in the image
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
def create_sensor_process_diagram():
    """Create the sensor process diagram exactly matching the provided image"""

    # Create figure with white background
    fig, ax = plt.subplots(figsize=(14, 10), dpi=300, facecolor='white')
    ax.set_facecolor('white')

    # Node positions to match the image layout exactly
    positions = {
        'Process Start': (1, 6),
        'Station 202': (3, 7),
        'Station 203': (6, 7),
        'Station 204': (9, 7),
        'Station 205': (3, 4.5),
        'Station 206': (6, 4.5),
        'Station 207': (9, 4.5),
        'Station 208': (3, 2),
        'Station 209': (6, 2),
        'Process End': (11, 1)
    }

    # Node properties (events and bottleneck status from image)
    node_data = {
        'Process Start': {'events': None, 'bottleneck': False, 'type': 'start'},
        'Station 202': {'events': 47559, 'bottleneck': True, 'type': 'station'},
        'Station 203': {'events': 55921, 'bottleneck': True, 'type': 'station'},
        'Station 204': {'events': 44746, 'bottleneck': True, 'type': 'station'},
        'Station 205': {'events': 52673, 'bottleneck': True, 'type': 'station'},
        'Station 206': {'events': 51387, 'bottleneck': False, 'type': 'station'},
        'Station 207': {'events': 63798, 'bottleneck': False, 'type': 'station'},
        'Station 208': {'events': 60281, 'bottleneck': False, 'type': 'station'},
        'Station 209': {'events': 14762, 'bottleneck': False, 'type': 'station'},
        'Process End': {'events': None, 'bottleneck': False, 'type': 'end'}
    }

    # Edge connections with weights and colors from the image (exact values)
    edges = [
        # From Process Start (green arrows)
        ('Process Start', 'Station 202', 4755, 'green'),
        ('Process Start', 'Station 203', 5692, 'green'),
        ('Process Start', 'Station 204', 4474, 'green'),

        # Between stations - red for bottleneck paths
        ('Station 202', 'Station 203', 829, 'red'),
        ('Station 203', 'Station 204', 9200, 'red'),
        ('Station 202', 'Station 205', 829, 'red'),
        ('Station 203', 'Station 206', 1485, 'red'),

        # Green connections (normal flow)
        ('Station 205', 'Station 206', 1408, 'green'),
        ('Station 206', 'Station 207', 8276, 'green'),
        ('Station 205', 'Station 208', 2579, 'green'),
        ('Station 206', 'Station 209', 7896, 'green'),
        ('Station 207', 'Station 209', 576, 'green'),
        ('Station 208', 'Station 209', 2579, 'green'),

        # To Process End (green arrows)
        ('Station 207', 'Process End', 6379, 'green'),
        ('Station 209', 'Process End', 6028, 'green'),
        ('Station 209', 'Process End', 1476, 'green'),
    ]

    # Draw edges first (so they appear behind nodes)
    for start, end, weight, color in edges:
        start_pos = positions[start]
        end_pos = positions[end]

        # Draw arrow
        ax.annotate('', xy=end_pos, xytext=start_pos,
                   arrowprops=dict(arrowstyle='->', color=color, lw=2.5, alpha=0.8))

        # Add weight label
        mid_x = (start_pos[0] + end_pos[0]) / 2
        mid_y = (start_pos[1] + end_pos[1]) / 2
        ax.text(mid_x, mid_y, str(weight), fontsize=9, fontweight='bold',
               ha='center', va='center', color='black',
               bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.9, edgecolor='none'))

    # Draw nodes
    for node, pos in positions.items():
        data = node_data[node]
        x, y = pos

        if data['type'] in ['start', 'end']:
            # Circular nodes for start/end
            circle = plt.Circle((x, y), 0.4, facecolor='#4A90E2', edgecolor='black', linewidth=2)
            ax.add_patch(circle)
            label = 'Process Start' if data['type'] == 'start' else 'Process End'
            ax.text(x, y, label, ha='center', va='center', fontsize=10,
                   fontweight='bold', color='white')
        else:
            # Rectangular nodes for stations
            width, height = 1.8, 1.0

            # Color based on bottleneck status
            if data['bottleneck']:
                facecolor = '#FFE6E6'  # Light red background
                edgecolor = '#FF4444'  # Red border
                text_color = '#CC0000'  # Dark red text
                border_width = 3
            else:
                facecolor = '#E6F3FF'  # Light blue background
                edgecolor = '#4A90E2'  # Blue border
                text_color = '#2E5C8A'  # Dark blue text
                border_width = 2

            # Draw rectangle
            rect = plt.Rectangle((x - width/2, y - height/2), width, height,
                               facecolor=facecolor, edgecolor=edgecolor,
                               linewidth=border_width, alpha=0.9)
            ax.add_patch(rect)

            # Add station name
            ax.text(x, y + 0.2, node, ha='center', va='center',
                   fontsize=11, fontweight='bold', color=text_color)

            # Add event count
            ax.text(x, y - 0.1, f"{data['events']:,} events", ha='center', va='center',
                   fontsize=9, color=text_color)

            # Add BOTTLENECK label for bottleneck stations
            if data['bottleneck']:
                ax.text(x, y - 0.35, "BOTTLENECK", ha='center', va='center',
                       fontsize=8, fontweight='bold', color='#CC0000')

    # Set axis limits and properties
    ax.set_xlim(-0.5, 12.5)
    ax.set_ylim(0.5, 8)
    ax.set_aspect('equal')
    ax.axis('off')

    # Add title
    ax.text(6, 8.5, 'Sensor Process Analysis - Process Discovery',
           ha='center', va='center', fontsize=16, fontweight='bold')

    plt.tight_layout()
    return fig, ax

if __name__ == "__main__":
    # Create and display the diagram
    fig, ax = create_sensor_process_diagram()

    # Save the diagram with high quality
    output_path = 'sensor_process_analysis_exact_match.png'
    fig.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"Network diagram saved as '{output_path}'")

    # Also save as PDF for vector graphics
    pdf_path = 'sensor_process_analysis_exact_match.pdf'
    fig.savefig(pdf_path, bbox_inches='tight', facecolor='white', edgecolor='none')
    print(f"Vector diagram saved as '{pdf_path}'")

    # Also show the diagram (comment out if running headless)
    plt.show()
