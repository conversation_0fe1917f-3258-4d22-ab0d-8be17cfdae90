"""
Network Diagram Generator for Process Discovery
Creates a network diagram similar to the sensor process analysis shown in the image
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

class ProcessNetworkDiagram:
    def __init__(self, figsize=(14, 10)):
        self.fig, self.ax = plt.subplots(figsize=figsize)
        self.G = nx.DiGraph()
        self.pos = {}
        self.node_colors = {}
        self.edge_colors = {}
        self.edge_weights = {}
        
    def add_station(self, station_id, position, events_count, is_bottleneck=False):
        """Add a station node to the network"""
        self.G.add_node(station_id, events=events_count, bottleneck=is_bottleneck)
        self.pos[station_id] = position
        
        # Set node color based on bottleneck status
        if is_bottleneck:
            self.node_colors[station_id] = '#FF6B6B'  # Red for bottlenecks
        else:
            self.node_colors[station_id] = '#4ECDC4'  # Blue/teal for normal stations
    
    def add_connection(self, from_station, to_station, flow_count, is_bottleneck_path=False):
        """Add a connection between stations"""
        self.G.add_edge(from_station, to_station, weight=flow_count)
        self.edge_weights[(from_station, to_station)] = flow_count
        
        # Set edge color based on bottleneck status
        if is_bottleneck_path:
            self.edge_colors[(from_station, to_station)] = '#FF6B6B'  # Red for bottleneck paths
        else:
            self.edge_colors[(from_station, to_station)] = '#2ECC71'  # Green for normal paths
    
    def draw_diagram(self, title="Sensor Process Analysis - Process Discovery"):
        """Draw the complete network diagram"""
        self.ax.clear()
        self.ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # Draw edges first (so they appear behind nodes)
        for edge in self.G.edges():
            from_pos = self.pos[edge[0]]
            to_pos = self.pos[edge[1]]
            
            # Get edge properties
            weight = self.edge_weights.get(edge, 0)
            color = self.edge_colors.get(edge, '#2ECC71')
            
            # Draw arrow
            self.ax.annotate('', xy=to_pos, xytext=from_pos,
                           arrowprops=dict(arrowstyle='->', color=color, lw=2))
            
            # Add weight label on edge
            mid_x = (from_pos[0] + to_pos[0]) / 2
            mid_y = (from_pos[1] + to_pos[1]) / 2
            self.ax.text(mid_x, mid_y, str(weight), fontsize=10, 
                        ha='center', va='center', 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Draw nodes
        for node in self.G.nodes():
            pos = self.pos[node]
            color = self.node_colors.get(node, '#4ECDC4')
            events = self.G.nodes[node].get('events', 0)
            is_bottleneck = self.G.nodes[node].get('bottleneck', False)
            
            # Create fancy box for station
            if node in ['Process Start', 'Process End']:
                # Circular nodes for start/end
                circle = plt.Circle(pos, 0.8, color=color, alpha=0.8)
                self.ax.add_patch(circle)
                self.ax.text(pos[0], pos[1], node, ha='center', va='center', 
                           fontweight='bold', fontsize=10, color='white')
            else:
                # Rectangular nodes for stations
                width, height = 2.5, 1.5
                rect = FancyBboxPatch((pos[0] - width/2, pos[1] - height/2), 
                                    width, height,
                                    boxstyle="round,pad=0.1",
                                    facecolor=color, 
                                    edgecolor='black',
                                    linewidth=2 if is_bottleneck else 1,
                                    alpha=0.8)
                self.ax.add_patch(rect)
                
                # Add station text
                self.ax.text(pos[0], pos[1] + 0.2, node, ha='center', va='center', 
                           fontweight='bold', fontsize=11, color='white')
                self.ax.text(pos[0], pos[1] - 0.2, f"{events} events", ha='center', va='center', 
                           fontsize=9, color='white')
                
                # Add bottleneck label if applicable
                if is_bottleneck:
                    self.ax.text(pos[0], pos[1] - 0.5, "BOTTLENECK", ha='center', va='center', 
                               fontsize=8, color='white', fontweight='bold')
        
        # Set axis properties
        self.ax.set_xlim(-2, 12)
        self.ax.set_ylim(-2, 8)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        
        # Add legend
        legend_elements = [
            patches.Patch(color='#FF6B6B', label='Bottleneck Stations'),
            patches.Patch(color='#4ECDC4', label='Normal Stations'),
            patches.Patch(color='#2ECC71', label='Normal Flow'),
            patches.Patch(color='#FF6B6B', label='Bottleneck Flow')
        ]
        self.ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
        
        plt.tight_layout()
        return self.fig

def create_sample_diagram():
    """Create a sample diagram based on the image provided"""
    diagram = ProcessNetworkDiagram()
    
    # Add stations with positions (x, y) and event counts
    diagram.add_station('Process Start', (0, 4), 0)
    diagram.add_station('Station 202', (3, 6), 47559, is_bottleneck=True)
    diagram.add_station('Station 203', (6, 6), 55921, is_bottleneck=True)
    diagram.add_station('Station 204', (9, 6), 44746, is_bottleneck=True)
    diagram.add_station('Station 205', (3, 3), 52673, is_bottleneck=True)
    diagram.add_station('Station 206', (6, 3), 51387)
    diagram.add_station('Station 207', (9, 3), 63798)
    diagram.add_station('Station 208', (3, 0), 60281)
    diagram.add_station('Station 209', (6, 0), 14762)
    diagram.add_station('Process End', (10, 1), 0)
    
    # Add connections with flow counts
    diagram.add_connection('Process Start', 'Station 202', 4755)
    diagram.add_connection('Station 202', 'Station 203', 5692, is_bottleneck_path=True)
    diagram.add_connection('Station 203', 'Station 204', 4474, is_bottleneck_path=True)
    diagram.add_connection('Station 204', 'Process End', 9200)
    diagram.add_connection('Station 202', 'Station 205', 829, is_bottleneck_path=True)
    diagram.add_connection('Station 203', 'Station 206', 1485, is_bottleneck_path=True)
    diagram.add_connection('Station 205', 'Station 206', 1408)
    diagram.add_connection('Station 206', 'Station 207', 8276)
    diagram.add_connection('Station 205', 'Station 208', 2579)
    diagram.add_connection('Station 206', 'Station 209', 1896)
    diagram.add_connection('Station 207', 'Station 209', 576)
    diagram.add_connection('Station 207', 'Process End', 6379)
    diagram.add_connection('Station 208', 'Station 209', 2579)
    diagram.add_connection('Station 209', 'Process End', 6028)
    diagram.add_connection('Station 209', 'Process End', 1476)
    
    return diagram

if __name__ == "__main__":
    # Create and display the diagram
    diagram = create_sample_diagram()
    fig = diagram.draw_diagram()

    # Save the diagram
    plt.savefig('process_network_diagram.png', dpi=300, bbox_inches='tight')
    print("Network diagram saved as 'process_network_diagram.png'")

    # Also show the diagram (comment out if running headless)
    plt.show()
