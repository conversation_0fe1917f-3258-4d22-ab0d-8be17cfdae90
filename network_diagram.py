"""
Network Diagram Generator for Process Discovery
Creates a network diagram similar to the sensor process analysis shown in the image
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
def create_sensor_process_diagram():
    """Create the sensor process diagram based on the provided image"""
    # ---------- Build the graph ----------
    G = nx.DiGraph()

    # Node definitions with positions and metadata
    nodes = {
        'Start': {'pos': (-1, 2), 'label': 'Process\nStart'},
        'Station 202': {'pos': (0, 2), 'events': 47559, 'bottleneck': True},
        'Station 203': {'pos': (1, 2), 'events': 55921, 'bottleneck': True},
        'Station 204': {'pos': (2, 2), 'events': 44746, 'bottleneck': True},
        'Station 205': {'pos': (0, 1), 'events': 52673, 'bottleneck': True},
        'Station 206': {'pos': (1, 1), 'events': 51387, 'bottleneck': False},
        'Station 207': {'pos': (2, 1), 'events': 63798, 'bottleneck': False},
        'Station 208': {'pos': (0, 0), 'events': 60281, 'bottleneck': False},
        'Station 209': {'pos': (1, 0), 'events': 14762, 'bottleneck': False},
        'End':   {'pos': (3, -1), 'label': 'Process\nEnd'}
    }

    # Add nodes to graph
    for n in nodes:
        G.add_node(n)

    # Edge definitions (weights pulled from the original figure and formatted w/ commas)
    edges = [
        ('Start', 'Station 202', 4755),
        ('Start', 'Station 203', 5592),
        ('Start', 'Station 204', 4474),
        ('Station 202', 'Station 206', 529),
        ('Station 203', 'Station 204', 9200),
        ('Station 203', 'Station 206', 8885),
        ('Station 205', 'Station 206', 2888),
        ('Station 206', 'Station 207', 8276),
        ('Station 206', 'Station 209', 7596),
        ('Station 207', 'Station 209', 576),
        ('Station 208', 'Station 209', 2529),
        ('Station 207', 'End', 6379),
        ('Station 209', 'End', 6028),
        ('Station 209', 'End', 1476),
    ]

    # Add edges to graph
    for u, v, w in edges:
        G.add_edge(u, v, weight=w)

    pos = {n: meta['pos'] for n, meta in nodes.items()}

    # ---------- Draw the figure ----------
    fig, ax = plt.subplots(figsize=(12, 7), dpi=300)

    # Draw edges with width scaled to weight
    for u, v, data in G.edges(data=True):
        weight = data['weight']
        nx.draw_networkx_edges(
            G, pos,
            edgelist=[(u, v)],
            arrowstyle='-|>',
            arrows=True,
            ax=ax,
            width=max(weight / 2000, 0.8),   # simple scaling for visibility
            connectionstyle='arc3,rad=0.0'
        )
        # Edge labels (formatted with comma as thousand separator)
        label = f"{weight:,}"
        x = (pos[u][0] + pos[v][0]) / 2
        y = (pos[u][1] + pos[v][1]) / 2
        ax.text(x, y, label, fontsize=7, ha='center', va='center')

    # Draw nodes
    for n, meta in nodes.items():
        x, y = meta['pos']
        # Start & End as circles
        if n in ('Start', 'End'):
            circle = plt.Circle((x, y), 0.12, edgecolor='black', facecolor='lightblue', lw=1.2)
            ax.add_patch(circle)
            ax.text(x, y, meta['label'], fontsize=8, ha='center', va='center')
        else:
            # Stations as rectangles, red outline for bottlenecks, blue otherwise
            facecolor = 'white'
            edgecolor = 'red' if meta['bottleneck'] else 'blue'
            bbox_props = dict(boxstyle="round,pad=0.3", fc=facecolor, ec=edgecolor, lw=1.4)
            label = f"{n}\n{meta['events']:,} events"
            ax.text(x, y, label, fontsize=8, ha='center', va='center', bbox=bbox_props)

    ax.axis('off')
    plt.tight_layout()

    return fig, ax

if __name__ == "__main__":
    # Create and display the diagram
    fig, ax = create_sensor_process_diagram()

    # Save the diagram
    output_path = 'sensor_process_analysis_redraw.png'
    fig.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Network diagram saved as '{output_path}'")

    # Also show the diagram (comment out if running headless)
    plt.show()
