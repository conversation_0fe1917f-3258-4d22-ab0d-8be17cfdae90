<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Process Analysis v13 - High Resolution</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 4px solid #667eea;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            color: #6c757d;
            margin: 10px 0 0 0;
            font-size: 1.2em;
        }
        
        .resolution-badge {
            display: inline-block;
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #dee2e6;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
        }
        
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
        }
        
        .controls {
            text-align: center;
            margin: 25px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 10px;
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #dc3545;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .info-card ul {
            color: #495057;
            line-height: 1.6;
        }
        
        .zoom-controls {
            margin: 15px 0;
        }
        
        .zoom-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .specs {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .specs h4 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        
        .specs p {
            color: #155724;
            margin: 5px 0;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sensor Process Analysis v13</h1>
            <p>High-Resolution Publication Quality</p>
            <div class="resolution-badge">🔥 400 DPI HIGH RESOLUTION</div>
        </div>
        
        <div class="specs">
            <h4>📊 Technical Specifications</h4>
            <p><strong>Resolution:</strong> 400 DPI (6400×4800 pixels) | <strong>Size:</strong> 16×12 inches</p>
            <p><strong>Formatting:</strong> MDPI compliant (commas for 5+ digit numbers) | <strong>Quality:</strong> Publication ready</p>
        </div>
        
        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                <button class="zoom-btn" onclick="fullscreen()">⛶ Fullscreen</button>
            </div>
            
            <img id="diagram" 
                 src="sensor_process_analysis_v13_high_res.png" 
                 alt="High-Resolution Sensor Process Analysis v13" 
                 class="diagram-image"
                 onclick="toggleFullscreen()">
        </div>
        
        <div class="controls">
            <a href="sensor_process_analysis_v13_high_res.png" download class="btn">📥 Download High-Res PNG</a>
            <a href="sensor_process_analysis_v13_high_res.pdf" download class="btn">📄 Download High-Res PDF</a>
            <a href="sensor_process_analysis_v13_high_res.svg" download class="btn">🎨 Download High-Res SVG</a>
            <a href="network_diagram_v13.py" download class="btn">💻 Download Code</a>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🎯 High-Resolution Features</h3>
                <ul>
                    <li><strong>400 DPI resolution:</strong> Crystal clear at any size</li>
                    <li><strong>16×12 inch canvas:</strong> Large format for detailed viewing</li>
                    <li><strong>Enhanced fonts:</strong> Size 12-14 for perfect legibility</li>
                    <li><strong>Thicker lines:</strong> 3.0-4.5px width for clarity</li>
                    <li><strong>Larger arrows:</strong> Size 30 for clear direction indication</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>📝 MDPI Compliance</h3>
                <ul>
                    <li><strong>Correct formatting:</strong> Commas for numbers ≥10,000</li>
                    <li><strong>Examples:</strong> "47,559 events", "8,947", "9,200"</li>
                    <li><strong>No commas for:</strong> Numbers with 4 digits (e.g., "2529")</li>
                    <li><strong>Consistent styling:</strong> All labels follow MDPI rules</li>
                    <li><strong>Publication ready:</strong> Meets journal standards</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🔧 Technical Excellence</h3>
                <ul>
                    <li><strong>Minimum 1000px:</strong> Exceeds requirement (6400×4800)</li>
                    <li><strong>Vector formats:</strong> PDF and SVG for infinite scaling</li>
                    <li><strong>Professional quality:</strong> Ready for print and digital</li>
                    <li><strong>Enhanced visibility:</strong> Bold fonts and clear contrasts</li>
                    <li><strong>Perfect legibility:</strong> All text clearly readable</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        const diagram = document.getElementById('diagram');
        
        function zoomIn() {
            currentZoom *= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            diagram.style.transform = 'scale(1)';
        }
        
        function fullscreen() {
            if (diagram.requestFullscreen) {
                diagram.requestFullscreen();
            } else if (diagram.webkitRequestFullscreen) {
                diagram.webkitRequestFullscreen();
            } else if (diagram.msRequestFullscreen) {
                diagram.msRequestFullscreen();
            }
        }
        
        function toggleFullscreen() {
            fullscreen();
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                zoomIn();
            } else if (e.key === '-') {
                e.preventDefault();
                zoomOut();
            } else if (e.key === '0') {
                e.preventDefault();
                resetZoom();
            } else if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                fullscreen();
            }
        });
        
        // Mouse wheel zoom
        diagram.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    </script>
</body>
</html>
