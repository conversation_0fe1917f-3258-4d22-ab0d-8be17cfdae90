<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Process Analysis - Professional Diagram</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 1.2em;
        }
        
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn.secondary {
            background: #95a5a6;
        }
        
        .btn.secondary:hover {
            background: #7f8c8d;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .info-card ul {
            color: #5a6c7d;
            line-height: 1.6;
        }
        
        .zoom-controls {
            margin: 15px 0;
        }
        
        .zoom-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .zoom-btn:hover {
            background: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sensor Process Analysis v6</h1>
            <p>Professional Process Discovery Diagram with Updated Flow Connections</p>
        </div>
        
        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                <button class="zoom-btn" onclick="fullscreen()">⛶ Fullscreen</button>
            </div>
            
            <img id="diagram"
                 src="sensor_process_analysis_v6.png"
                 alt="Sensor Process Analysis Diagram v6"
                 class="diagram-image"
                 onclick="toggleFullscreen()">
        </div>
        
        <div class="controls">
            <a href="sensor_process_analysis_v6.png" download class="btn">📥 Download PNG v6</a>
            <a href="sensor_process_analysis_v6.pdf" download class="btn">📄 Download PDF v6</a>
            <a href="sensor_process_analysis_v6.svg" download class="btn">🎨 Download SVG v6</a>
            <a href="network_diagram_v6.py" download class="btn secondary">💻 Download Code v6</a>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🎨 Professional Features</h3>
                <ul>
                    <li>Curved connection lines for elegant flow</li>
                    <li>3D shadow effects for depth</li>
                    <li>Professional color scheme</li>
                    <li>Enhanced typography</li>
                    <li>Smart label positioning</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>📊 Technical Details</h3>
                <ul>
                    <li>High-resolution 300 DPI output</li>
                    <li>Vector graphics (PDF/SVG) available</li>
                    <li>Exact data from original image</li>
                    <li>Bottleneck identification</li>
                    <li>Flow analysis with precise numbers</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🚀 Usage</h3>
                <ul>
                    <li>Executive presentations</li>
                    <li>Technical documentation</li>
                    <li>Process improvement reports</li>
                    <li>Academic publications</li>
                    <li>Corporate dashboards</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        const diagram = document.getElementById('diagram');
        
        function zoomIn() {
            currentZoom *= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            diagram.style.transform = 'scale(1)';
        }
        
        function fullscreen() {
            if (diagram.requestFullscreen) {
                diagram.requestFullscreen();
            } else if (diagram.webkitRequestFullscreen) {
                diagram.webkitRequestFullscreen();
            } else if (diagram.msRequestFullscreen) {
                diagram.msRequestFullscreen();
            }
        }
        
        function toggleFullscreen() {
            fullscreen();
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                zoomIn();
            } else if (e.key === '-') {
                e.preventDefault();
                zoomOut();
            } else if (e.key === '0') {
                e.preventDefault();
                resetZoom();
            } else if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                fullscreen();
            }
        });
        
        // Mouse wheel zoom
        diagram.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    </script>
</body>
</html>
