<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Process Analysis - Engineering Layout v6</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.2em;
            font-weight: 600;
        }
        
        .header p {
            color: #6c757d;
            margin: 8px 0 0 0;
            font-size: 1.1em;
        }
        
        .engineering-badge {
            display: inline-block;
            background: #17a2b8;
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            margin-top: 8px;
        }
        
        .diagram-container {
            text-align: center;
            margin: 25px 0;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 8px;
            background: #2c3e50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background 0.3s ease;
            font-size: 0.9em;
        }
        
        .btn:hover {
            background: #34495e;
        }
        
        .btn.engineering {
            background: #17a2b8;
        }
        
        .btn.engineering:hover {
            background: #138496;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 18px;
            margin-top: 25px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 18px;
            border-radius: 6px;
            border-left: 4px solid #2c3e50;
        }
        
        .info-card.engineering {
            border-left-color: #17a2b8;
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.1em;
        }
        
        .info-card ul {
            color: #495057;
            line-height: 1.5;
            font-size: 0.9em;
        }
        
        .zoom-controls {
            margin: 12px 0;
        }
        
        .zoom-btn {
            padding: 6px 12px;
            margin: 0 4px;
            background: #495057;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .zoom-btn:hover {
            background: #343a40;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sensor Process Analysis v6</h1>
            <p>Engineering-Optimized Layout with Zero Overlaps</p>
            <div class="engineering-badge">🔧 ENGINEERING LAYOUT</div>
        </div>
        
        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                <button class="zoom-btn" onclick="fullscreen()">⛶ Fullscreen</button>
            </div>
            
            <img id="diagram" 
                 src="sensor_process_analysis_v6_engineering.png" 
                 alt="Engineering-Optimized Sensor Process Analysis v6" 
                 class="diagram-image"
                 onclick="toggleFullscreen()">
        </div>
        
        <div class="controls">
            <a href="sensor_process_analysis_v6_engineering.png" download class="btn engineering">📥 Download PNG</a>
            <a href="sensor_process_analysis_v6_engineering.pdf" download class="btn engineering">📄 Download PDF</a>
            <a href="sensor_process_analysis_v6_engineering.svg" download class="btn engineering">🎨 Download SVG</a>
            <a href="network_diagram_v6.py" download class="btn">💻 Download Code</a>
        </div>
        
        <div class="info-grid">
            <div class="info-card engineering">
                <h3>🔧 Engineering Optimizations</h3>
                <ul>
                    <li><strong>Zero overlaps:</strong> Proper spacing between all elements</li>
                    <li><strong>Compact stations:</strong> S202, S203 format for clarity</li>
                    <li><strong>Strategic curves:</strong> Intelligent path routing</li>
                    <li><strong>Optimal sizing:</strong> 14x8 professional dimensions</li>
                    <li><strong>Clear hierarchy:</strong> Flow thickness by volume</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>📐 Layout Principles</h3>
                <ul>
                    <li>3-unit spacing between station rows</li>
                    <li>2.5-unit spacing between columns</li>
                    <li>Curved connections avoid intersections</li>
                    <li>Labels positioned for maximum readability</li>
                    <li>Professional engineering standards applied</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🎯 Visibility Features</h3>
                <ul>
                    <li><strong>High contrast:</strong> Professional color scheme</li>
                    <li><strong>Readable labels:</strong> Inside flow lines with borders</li>
                    <li><strong>Clear bottlenecks:</strong> Red stations and flows</li>
                    <li><strong>Flow scaling:</strong> Line thickness by volume</li>
                    <li><strong>Grid reference:</strong> Subtle engineering grid</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        const diagram = document.getElementById('diagram');
        
        function zoomIn() {
            currentZoom *= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            diagram.style.transform = 'scale(1)';
        }
        
        function fullscreen() {
            if (diagram.requestFullscreen) {
                diagram.requestFullscreen();
            } else if (diagram.webkitRequestFullscreen) {
                diagram.webkitRequestFullscreen();
            } else if (diagram.msRequestFullscreen) {
                diagram.msRequestFullscreen();
            }
        }
        
        function toggleFullscreen() {
            fullscreen();
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                zoomIn();
            } else if (e.key === '-') {
                e.preventDefault();
                zoomOut();
            } else if (e.key === '0') {
                e.preventDefault();
                resetZoom();
            } else if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                fullscreen();
            }
        });
        
        // Mouse wheel zoom
        diagram.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    </script>
</body>
</html>
