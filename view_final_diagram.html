<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Process Analysis v13 - Final Optimized</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1300px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 4px solid #667eea;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            color: #6c757d;
            margin: 10px 0 0 0;
            font-size: 1.2em;
        }
        
        .final-badge {
            display: inline-block;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .diagram-container {
            text-align: center;
            margin: 30px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 25px;
            border: 2px solid #dee2e6;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
        }
        
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .diagram-image:hover {
            transform: scale(1.02);
        }
        
        .controls {
            text-align: center;
            margin: 25px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn.success:hover {
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .info-card.final {
            border-left-color: #28a745;
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .info-card ul {
            color: #495057;
            line-height: 1.6;
        }
        
        .zoom-controls {
            margin: 15px 0;
        }
        
        .zoom-btn {
            padding: 8px 16px;
            margin: 0 5px;
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sensor Process Analysis v13</h1>
            <p>Final Optimized Layout with Perfect Visibility</p>
            <div class="final-badge">✨ FINAL OPTIMIZED VERSION</div>
        </div>
        
        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                <button class="zoom-btn" onclick="fullscreen()">⛶ Fullscreen</button>
            </div>
            
            <img id="diagram" 
                 src="sensor_process_analysis_v13_final.png" 
                 alt="Final Optimized Sensor Process Analysis v13" 
                 class="diagram-image"
                 onclick="toggleFullscreen()">
        </div>
        
        <div class="controls">
            <a href="sensor_process_analysis_v13_final.png" download class="btn success">📥 Download Final PNG</a>
            <a href="sensor_process_analysis_v13_final.pdf" download class="btn success">📄 Download Final PDF</a>
            <a href="sensor_process_analysis_v13_final.svg" download class="btn success">🎨 Download Final SVG</a>
            <a href="network_diagram_v13.py" download class="btn">💻 Download Final Code</a>
        </div>
        
        <div class="info-grid">
            <div class="info-card final">
                <h3>🎯 Final Optimizations</h3>
                <ul>
                    <li><strong>Perfect label positioning:</strong> Mathematical curve offset calculation</li>
                    <li><strong>Zero overlaps:</strong> Optimized node spacing and edge routing</li>
                    <li><strong>Smart edge widths:</strong> Flow volume-based thickness (2.5-3.5px)</li>
                    <li><strong>Enhanced readability:</strong> Larger nodes with proper padding</li>
                    <li><strong>Professional arrows:</strong> Size 22 with clear visibility</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>🔧 Technical Excellence</h3>
                <ul>
                    <li>Mathematical label positioning using perpendicular vectors</li>
                    <li>Dynamic offset calculation based on curve radius</li>
                    <li>Optimized figure size (14x8) for presentations</li>
                    <li>High-quality 300 DPI output for printing</li>
                    <li>Clean code structure for easy modifications</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3>📊 Process Flow Features</h3>
                <ul>
                    <li><strong>Green flows:</strong> Normal process paths with labels</li>
                    <li><strong>Red flows:</strong> Bottleneck paths (labeled and unlabeled)</li>
                    <li><strong>Smart curves:</strong> Prevent line intersections</li>
                    <li><strong>MDPI formatting:</strong> Thousands separators for large numbers</li>
                    <li><strong>Color coding:</strong> Red stations = bottlenecks, Blue = normal</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        const diagram = document.getElementById('diagram');
        
        function zoomIn() {
            currentZoom *= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function zoomOut() {
            currentZoom /= 1.2;
            diagram.style.transform = `scale(${currentZoom})`;
        }
        
        function resetZoom() {
            currentZoom = 1;
            diagram.style.transform = 'scale(1)';
        }
        
        function fullscreen() {
            if (diagram.requestFullscreen) {
                diagram.requestFullscreen();
            } else if (diagram.webkitRequestFullscreen) {
                diagram.webkitRequestFullscreen();
            } else if (diagram.msRequestFullscreen) {
                diagram.msRequestFullscreen();
            }
        }
        
        function toggleFullscreen() {
            fullscreen();
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                zoomIn();
            } else if (e.key === '-') {
                e.preventDefault();
                zoomOut();
            } else if (e.key === '0') {
                e.preventDefault();
                resetZoom();
            } else if (e.key === 'f' || e.key === 'F') {
                e.preventDefault();
                fullscreen();
            }
        });
        
        // Mouse wheel zoom
        diagram.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    </script>
</body>
</html>
