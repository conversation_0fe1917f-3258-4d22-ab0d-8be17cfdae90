import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import math
import numpy as np

class InteractiveNetworkDiagram:
    def __init__(self):
        self.fig, self.ax = plt.subplots(figsize=(18, 8), dpi=100)
        self.ax.set_xlim(-3, 7)
        self.ax.set_ylim(-1, 3)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        
        # Node positions (editable)
        self.pos = {
            'Start': (-2, 2),
            'Station 202': (0, 2),
            'Station 203': (2, 2),
            'Station 204': (4, 2),
            'Station 205': (0, 1),
            'Station 206': (2, 1),
            'Station 207': (4, 1),
            'Station 208': (0, 0),
            'Station 209': (2, 0),
            'End': (6, 0)
        }
        
        # Node data
        self.node_data = {
            'Start': {'type': 'start', 'events': None},
            'Station 202': {'type': 'station', 'events': 47559, 'bottleneck': True},
            'Station 203': {'type': 'station', 'events': 55921, 'bottleneck': True},
            'Station 204': {'type': 'station', 'events': 44746, 'bottleneck': True},
            'Station 205': {'type': 'station', 'events': 52673, 'bottleneck': True},
            'Station 206': {'type': 'station', 'events': 51387, 'bottleneck': False},
            'Station 207': {'type': 'station', 'events': 63798, 'bottleneck': False},
            'Station 208': {'type': 'station', 'events': 60281, 'bottleneck': False},
            'Station 209': {'type': 'station', 'events': 14762, 'bottleneck': False},
            'End': {'type': 'end', 'events': None}
        }
        
        # Edge definitions
        self.edges = [
            ('Start', 'Station 202', 4755, 'green', -0.1, True),
            ('Start', 'Station 203', 5592, 'green', 0.0, True),
            ('Start', 'Station 204', 4474, 'green', 0.15, True),
            ('Station 206', 'Station 207', 8276, 'green', 0.0, True),
            ('Station 208', 'Station 207', 7596, 'green', 0.1, True),
            ('Station 208', 'Station 209', 2529, 'green', 0.0, True),
            ('Station 208', 'End', 6028, 'green', 0.2, True),
            ('Station 209', 'End', 1476, 'green', -0.15, True),
            ('Station 207', 'End', 6379, 'green', 0.0, True),
            ('Station 208', 'Station 205', None, 'red', -0.1, False),
            ('Station 205', 'Station 202', None, 'red', 0.1, False),
            ('Station 202', 'Station 209', None, 'red', 0.2, False),
            ('Station 202', 'Station 207', None, 'red', 0.25, False),
            ('Station 206', 'Station 203', None, 'red', 0.15, False),
            ('Station 209', 'Station 206', None, 'red', -0.2, False),
            ('Station 205', 'Station 204', 8485, 'red', -0.1, True),
            ('Station 205', 'Station 203', 529, 'red', 0.1, True),
            ('Station 202', 'Station 203', 8947, 'red', -0.05, True),
            ('Station 203', 'Station 204', 9200, 'red', 0.0, True),
            ('Station 202', 'Station 206', 529, 'green', 0.0, True),
            ('Station 205', 'Station 206', 2888, 'green', 0.0, True),
            ('Station 207', 'Station 209', 576, 'green', 0.0, True),
        ]
        
        # Interactive state
        self.selected_node = None
        self.offset = (0, 0)
        self.node_artists = {}
        self.text_artists = {}
        
        # Connect events
        self.fig.canvas.mpl_connect('button_press_event', self.on_press)
        self.fig.canvas.mpl_connect('button_release_event', self.on_release)
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_motion)
        
        self.draw_diagram()
        
    def fmt(self, num):
        if num is None:
            return ""
        return f"{num:,}" if abs(num) >= 10000 else str(num)
    
    def draw_diagram(self):
        self.ax.clear()
        self.ax.set_xlim(-3, 7)
        self.ax.set_ylim(-1, 3)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        
        # Clear artists dictionaries
        self.node_artists.clear()
        self.text_artists.clear()
        
        # Draw edges first
        for u, v, weight, color, rad, show_label in self.edges:
            self.draw_edge(u, v, weight, color, rad, show_label)
        
        # Draw nodes
        for node, (x, y) in self.pos.items():
            self.draw_node(node, x, y)
        
        # Add title
        self.ax.text(2, 2.7, 'Interactive Sensor Process Analysis - Drag & Drop Enabled', 
                    fontsize=14, ha='center', va='center', fontweight='bold', color='#2C3E50')
        
        self.fig.canvas.draw()
    
    def draw_edge(self, u, v, weight, color, rad, show_label):
        start_pos = self.pos[u]
        end_pos = self.pos[v]
        
        # Draw arrow
        if abs(rad) > 0.01:
            connectionstyle = f"arc3,rad={rad}"
        else:
            connectionstyle = "arc3,rad=0"
        
        self.ax.annotate('', xy=end_pos, xytext=start_pos,
                        arrowprops=dict(arrowstyle='->', color=color, lw=2.5,
                                      connectionstyle=connectionstyle, alpha=0.8))
        
        # Add label if needed
        if show_label and weight is not None:
            xm = (start_pos[0] + end_pos[0]) / 2
            ym = (start_pos[1] + end_pos[1]) / 2
            
            if abs(rad) > 0.01:
                dx = end_pos[0] - start_pos[0]
                dy = end_pos[1] - start_pos[1]
                length = math.hypot(dx, dy) or 1e-5
                px, py = -dy / length, dx / length
                offset = rad * length * 0.5
                xlab = xm + px * offset
                ylab = ym + py * offset
            else:
                xlab = xm
                ylab = ym
            
            self.ax.text(xlab, ylab, self.fmt(weight), fontsize=9, ha='center', va='center',
                        fontweight='bold', color='white',
                        bbox=dict(boxstyle="round,pad=0.2", facecolor=color,
                                edgecolor='black', linewidth=1, alpha=0.9))
    
    def draw_node(self, node, x, y):
        data = self.node_data[node]
        
        if data['type'] in ['start', 'end']:
            # Circular nodes
            circle = plt.Circle((x, y), 0.4, ec='#2C3E50', fc='#3498DB', lw=3, picker=True)
            self.ax.add_patch(circle)
            self.node_artists[node] = circle
            
            label = 'Process\n' + ('Start' if data['type'] == 'start' else 'End')
            text = self.ax.text(x, y, label, fontsize=11, ha='center', va='center',
                               fontweight='bold', color='white', picker=True)
            self.text_artists[node] = text
        else:
            # Station nodes
            bottleneck = data['bottleneck']
            ec = '#E74C3C' if bottleneck else '#3498DB'
            fc = '#FADBD8' if bottleneck else '#EBF5FB'
            text_color = '#C0392B' if bottleneck else '#2E86AB'
            
            # Create rectangle (will be converted to bbox in text)
            label = f"{node}\n{self.fmt(data['events'])} events"
            text = self.ax.text(x, y, label, fontsize=10, ha='center', va='center',
                               bbox=dict(boxstyle="round,pad=1.0", fc=fc, ec=ec, lw=2),
                               fontweight='bold', color=text_color, picker=True)
            self.text_artists[node] = text
            self.node_artists[node] = text  # For stations, text is the main artist
    
    def get_node_at_position(self, x, y):
        for node, (nx, ny) in self.pos.items():
            distance = math.sqrt((x - nx)**2 + (y - ny)**2)
            if distance < 0.5:  # Within node radius
                return node
        return None
    
    def on_press(self, event):
        if event.inaxes != self.ax:
            return
        
        self.selected_node = self.get_node_at_position(event.xdata, event.ydata)
        if self.selected_node:
            node_pos = self.pos[self.selected_node]
            self.offset = (event.xdata - node_pos[0], event.ydata - node_pos[1])
            print(f"Selected: {self.selected_node}")
    
    def on_motion(self, event):
        if self.selected_node is None or event.inaxes != self.ax:
            return
        
        # Update position
        new_x = event.xdata - self.offset[0]
        new_y = event.ydata - self.offset[1]
        self.pos[self.selected_node] = (new_x, new_y)
        
        # Redraw diagram
        self.draw_diagram()
    
    def on_release(self, event):
        if self.selected_node:
            print(f"Released: {self.selected_node} at position {self.pos[self.selected_node]}")
        self.selected_node = None
        self.offset = (0, 0)
    
    def save_diagram(self, filename='interactive_sensor_process.png'):
        self.fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Diagram saved as '{filename}'")
    
    def print_positions(self):
        print("\nCurrent node positions:")
        for node, pos in self.pos.items():
            print(f"'{node}': {pos}")

# Create and run the interactive diagram
if __name__ == "__main__":
    print("🎯 Interactive Network Diagram")
    print("📌 Instructions:")
    print("   • Click and drag any node to move it")
    print("   • Connections will update automatically")
    print("   • Press 's' to save current layout")
    print("   • Press 'p' to print current positions")
    print("   • Close window when done")
    
    diagram = InteractiveNetworkDiagram()
    
    def on_key(event):
        if event.key == 's':
            diagram.save_diagram()
        elif event.key == 'p':
            diagram.print_positions()
    
    diagram.fig.canvas.mpl_connect('key_press_event', on_key)
    plt.show()
