import networkx as nx
import matplotlib.pyplot as plt

def fmt(num: int | None) -> str:
    """Format numbers with thousands separator for readability (MDPI rule)"""
    if num is None:
        return ""
    return f"{num:,}" if len(str(abs(num))) >= 5 else str(num)

# ------------------------------------------------------------------------------
# Create MultiDiGraph to handle multiple edges between same nodes
G = nx.MultiDiGraph()

# Redesigned layout with proper engineering spacing to avoid overlaps
nodes = {
    'Start':      {'pos': (0,   6), 'label': 'Process\nStart'},
    'Station 202':{'pos': (2,   5), 'events': 47_559, 'bottleneck': True},
    'Station 203':{'pos': (5,   5), 'events': 55_921, 'bottleneck': True},
    'Station 204':{'pos': (8,   5), 'events': 44_746, 'bottleneck': True},
    'Station 205':{'pos': (2,   3), 'events': 52_673, 'bottleneck': True},
    'Station 206':{'pos': (5,   3), 'events': 51_387, 'bottleneck': False},
    'Station 207':{'pos': (8,   3), 'events': 63_798, 'bottleneck': False},
    'Station 208':{'pos': (2,   1), 'events': 60_281, 'bottleneck': False},
    'Station 209':{'pos': (5,   1), 'events': 14_762, 'bottleneck': False},
    'End':        {'pos': (10,  0), 'label': 'Process\nEnd'}
}

for n in nodes:
    G.add_node(n)

# Redesigned flow connections with intelligent curve management to avoid overlaps
edges_info = [
    # --- Green flows (normal process paths) - optimized curves ---
    dict(u='Start',        v='Station 202', w=4_755, color='#2E8B57', rad=-0.15, label=True),
    dict(u='Start',        v='Station 203', w=5_592, color='#2E8B57', rad= 0.00, label=True),
    dict(u='Start',        v='Station 204', w=4_474, color='#2E8B57', rad= 0.15, label=True),
    dict(u='Station 206',  v='Station 207', w=8_276, color='#2E8B57', rad= 0.00, label=True),
    dict(u='Station 208',  v='Station 207', w=7_596, color='#2E8B57', rad= 0.20, label=True),
    dict(u='Station 208',  v='Station 209', w=2_529, color='#2E8B57', rad= 0.00, label=True),
    dict(u='Station 208',  v='End',         w=6_028, color='#2E8B57', rad= 0.30, label=True),
    dict(u='Station 209',  v='End',         w=1_476, color='#2E8B57', rad=-0.20, label=True),
    dict(u='Station 207',  v='End',         w=6_379, color='#2E8B57', rad= 0.10, label=True),

    # --- Red flows (bottleneck paths) - strategic curves to avoid overlaps ---
    dict(u='Station 208',  v='Station 205', w=None,  color='#DC143C', rad=-0.25, label=False),
    dict(u='Station 205',  v='Station 202', w=None,  color='#DC143C', rad= 0.00, label=False),
    dict(u='Station 205',  v='Station 204', w=8_485, color='#DC143C', rad= 0.25, label=True),
    dict(u='Station 205',  v='Station 203', w=  529, color='#DC143C', rad= 0.00, label=True),
    dict(u='Station 202',  v='Station 209', w=None,  color='#DC143C', rad= 0.40, label=False),
    dict(u='Station 202',  v='Station 207', w=None,  color='#DC143C', rad= 0.35, label=False),
    dict(u='Station 202',  v='Station 203', w=8_947, color='#DC143C', rad= 0.00, label=True),
    dict(u='Station 203',  v='Station 204', w=9_200, color='#DC143C', rad= 0.00, label=True),
    dict(u='Station 206',  v='Station 203', w=None,  color='#DC143C', rad=-0.20, label=False),
    dict(u='Station 209',  v='Station 206', w=None,  color='#DC143C', rad=-0.30, label=False),

    # --- Neutral flows (essential connections) ---
    dict(u='Station 202',  v='Station 206', w= 529,  color='#7F8C8D', rad= 0.00, label=True),
    dict(u='Station 205',  v='Station 206', w=2_888, color='#7F8C8D', rad= 0.00, label=True),
]

for e in edges_info:
    G.add_edge(
        e['u'], e['v'],
        weight=e['w'],
        color=e['color'],
        rad=e['rad'],
        show_label=e['label']
    )

pos = {n: meta['pos'] for n, meta in nodes.items()}

# ------------------------------------------------------------------------------
# Professional engineering diagram with optimal size and spacing
fig, ax = plt.subplots(figsize=(14, 8), dpi=300, facecolor='white')
ax.set_facecolor('#FAFAFA')  # Very light background for professional look

for u, v, key, data in G.edges(keys=True, data=True):
    w          = data['weight']
    color      = data['color']
    rad        = data['rad']
    show_label = data['show_label']

    # Engineering-optimized line thickness based on flow volume
    if w:
        width = max(min(w / 2000, 4.0), 1.2)  # Scale between 1.2 and 4.0
    else:
        width = 1.0  # Thinner for unlabeled flows

    nx.draw_networkx_edges(
        G, pos,
        edgelist=[(u, v)],
        ax=ax,
        arrowstyle='-|>',
        arrows=True,
        edge_color=color,
        width=width,
        connectionstyle=f'arc3,rad={rad}',
        min_target_margin=15,  # Proper clearance from nodes
        alpha=0.85,
        arrowsize=12
    )

    if show_label and w is not None:
        # Smart label positioning to avoid overlaps
        x = (pos[u][0] + pos[v][0]) / 2
        y = (pos[u][1] + pos[v][1]) / 2 + (rad * 0.4)

        # Professional label styling with high contrast
        label_color = 'white' if color in ['#2E8B57', '#DC143C'] else 'black'
        ax.text(x, y, fmt(w), fontsize=9, ha='center', va='center',
               fontweight='bold', color=label_color,
               bbox=dict(boxstyle="round,pad=0.25", facecolor=color, alpha=0.9,
                        edgecolor='white', linewidth=1))

# Draw nodes with engineering-standard sizing and spacing
for n, meta in nodes.items():
    x, y = meta['pos']
    if n in ('Start', 'End'):
        # Professional start/end nodes
        circ = plt.Circle((x, y), 0.3, edgecolor='#2C3E50', facecolor='#3498DB', lw=2.5)
        ax.add_patch(circ)
        ax.text(x, y, meta['label'], fontsize=10, ha='center', va='center',
               fontweight='bold', color='white')
    else:
        # Process stations with proper sizing to avoid overlaps
        edgecolor = '#E74C3C' if meta['bottleneck'] else '#3498DB'
        facecolor = '#FADBD8' if meta['bottleneck'] else '#EBF5FB'

        # Compact but readable station labels
        station_num = n.split()[-1]  # Extract just the number
        node_label = f"S{station_num}\n{fmt(meta['events'])}"
        text_color = '#C0392B' if meta['bottleneck'] else '#2E86AB'

        ax.text(x, y, node_label, fontsize=10, ha='center', va='center',
               bbox=dict(boxstyle="round,pad=0.3", fc=facecolor, ec=edgecolor, lw=2),
               fontweight='bold', color=text_color)

# Professional engineering diagram layout
ax.set_title('Sensor Process Flow Analysis - Engineering Layout v6',
            fontsize=14, fontweight='bold', color='#2C3E50', pad=15)

# Optimized axis limits to eliminate overlaps and ensure visibility
ax.set_xlim(-0.5, 11)
ax.set_ylim(-0.5, 6.5)
ax.set_aspect('equal')
ax.axis('off')

# Add subtle grid for professional engineering appearance
ax.grid(True, alpha=0.1, color='gray', linestyle='-', linewidth=0.5)
plt.tight_layout(pad=1.0)

# Save the engineering-optimized diagram
out_path = 'sensor_process_analysis_v6_engineering.png'
fig.savefig(out_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Engineering-optimized Sensor Process Analysis v6 saved as '{out_path}'")

# Also save as PDF and SVG for professional use
pdf_path = 'sensor_process_analysis_v6_engineering.pdf'
fig.savefig(pdf_path, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Engineering PDF version saved as '{pdf_path}'")

svg_path = 'sensor_process_analysis_v6_engineering.svg'
fig.savefig(svg_path, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Engineering SVG version saved as '{svg_path}'")

plt.show()
