import networkx as nx
import matplotlib.pyplot as plt

def fmt(num: int | None) -> str:
    """Format numbers with thousands separator for readability (MDPI rule)"""
    if num is None:
        return ""
    return f"{num:,}" if len(str(abs(num))) >= 5 else str(num)

# ------------------------------------------------------------------------------
# Create MultiDiGraph to handle multiple edges between same nodes
G = nx.MultiDiGraph()

nodes = {
    'Start':      {'pos': (-1,  2), 'label': 'Process\nStart'},
    'Station 202':{'pos': ( 0,  2), 'events': 47_559, 'bottleneck': True},
    'Station 203':{'pos': ( 1,  2), 'events': 55_921, 'bottleneck': True},
    'Station 204':{'pos': ( 2,  2), 'events': 44_746, 'bottleneck': True},
    'Station 205':{'pos': ( 0,  1), 'events': 52_673, 'bottleneck': True},
    'Station 206':{'pos': ( 1,  1), 'events': 51_387, 'bottleneck': False},
    'Station 207':{'pos': ( 2,  1), 'events': 63_798, 'bottleneck': False},
    'Station 208':{'pos': ( 0,  0), 'events': 60_281, 'bottleneck': False},
    'Station 209':{'pos': ( 1,  0), 'events': 14_762, 'bottleneck': False},
    'End':        {'pos': ( 3, -1), 'label': 'Process\nEnd'}
}

for n in nodes:
    G.add_node(n)

edges_info = [
    # --- Green flows -----------------------------------------------------------
    dict(u='Start',   v='Station 202', w=4_755, color='green', rad=-0.25, label=True),
    dict(u='Start',   v='Station 203', w=5_592, color='green', rad= 0.25, label=True),
    dict(u='Start',   v='Station 204', w=4_474, color='green', rad= 0.45, label=True),
    dict(u='Station 206', v='Station 207', w=8_276, color='green', rad=0.00, label=True),
    dict(u='Station 208', v='Station 207', w=7_596, color='green', rad=0.10, label=True),
    dict(u='Station 208', v='Station 209', w=2_529, color='green', rad=0.00, label=True),
    dict(u='Station 208', v='End',        w=6_028, color='green', rad= 0.55, label=True),
    dict(u='Station 209', v='End',        w=1_476, color='green', rad=-0.35, label=True),
    dict(u='Station 207', v='End',        w=6_379, color='green', rad=0.00, label=True),

    # --- Red flows -------------------------------------------------------------
    dict(u='Station 208', v='Station 205', w=None,  color='red',   rad=-0.20, label=False),
    dict(u='Station 205', v='Station 202', w=None,  color='red',   rad= 0.20, label=False),
    dict(u='Station 205', v='Station 204', w=8_485, color='red',   rad=-0.15, label=True),
    dict(u='Station 205', v='Station 203', w=  529, color='red',   rad= 0.15, label=True),
    dict(u='Station 202', v='Station 209', w=None,  color='red',   rad= 0.30, label=False),
    dict(u='Station 202', v='Station 207', w=None,  color='red',   rad= 0.35, label=False),
    dict(u='Station 202', v='Station 203', w=8_947, color='red',   rad=-0.15, label=True),
    dict(u='Station 203', v='Station 204', w=9_200, color='red',   rad= 0.00, label=True),
    dict(u='Station 206', v='Station 203', w=None,  color='red',   rad= 0.25, label=False),
    dict(u='Station 209', v='Station 206', w=None,  color='red',   rad=-0.25, label=False),

    # --- Black (unchanged) - removed connections between 203,206 and 209 as requested
    dict(u='Station 202', v='Station 206', w= 529,  color='black', rad=0.00, label=True),
    dict(u='Station 205', v='Station 206', w=2_888, color='black', rad=0.00, label=True),
]

for e in edges_info:
    G.add_edge(
        e['u'], e['v'],
        weight=e['w'],
        color=e['color'],
        rad=e['rad'],
        show_label=e['label']
    )

pos = {n: meta['pos'] for n, meta in nodes.items()}

# ------------------------------------------------------------------------------
# Draw with optimized layout for better visibility
fig, ax = plt.subplots(figsize=(16, 10), dpi=300, facecolor='white')
ax.set_facecolor('white')

for u, v, key, data in G.edges(keys=True, data=True):
    w          = data['weight']
    color      = data['color']
    rad        = data['rad']
    show_label = data['show_label']

    # Improved width scaling for better visibility
    width = max(w / 1500, 1.0) if w else 1.5

    nx.draw_networkx_edges(
        G, pos,
        edgelist=[(u, v)],
        ax=ax,
        arrowstyle='-|>',
        arrows=True,
        edge_color=color,
        width=width,
        connectionstyle=f'arc3,rad={rad}',
        min_target_margin=12,  # Increased margin for better node separation
        alpha=0.8
    )

    if show_label and w is not None:
        # Calculate label position along the curve for better visibility
        x = (pos[u][0] + pos[v][0]) / 2
        y = (pos[u][1] + pos[v][1]) / 2 + (rad * 0.3)  # Reduced offset for better positioning

        # Add label with enhanced styling for better visibility
        ax.text(x, y, fmt(w), fontsize=8, ha='center', va='center',
               fontweight='bold', color='white',
               bbox=dict(boxstyle="round,pad=0.2", facecolor=color, alpha=0.8, edgecolor='none'))

# Draw nodes with improved visibility
for n, meta in nodes.items():
    x, y = meta['pos']
    if n in ('Start', 'End'):
        # Enhanced circular nodes
        circ = plt.Circle((x, y), 0.15, edgecolor='#2C3E50', facecolor='#3498DB', lw=2)
        ax.add_patch(circ)
        ax.text(x, y, meta['label'], fontsize=9, ha='center', va='center',
               fontweight='bold', color='white')
    else:
        # Enhanced rectangular nodes with better contrast
        edgecolor = '#E74C3C' if meta['bottleneck'] else '#3498DB'  # Professional colors
        facecolor = '#FADBD8' if meta['bottleneck'] else '#EBF5FB'  # Light backgrounds
        bbox = dict(boxstyle="round,pad=0.4", fc=facecolor, ec=edgecolor, lw=2)
        node_label = f"{n}\n{fmt(meta['events'])} events"
        text_color = '#C0392B' if meta['bottleneck'] else '#2E86AB'
        ax.text(x, y, node_label, fontsize=9, ha='center', va='center',
               bbox=bbox, fontweight='bold', color=text_color)

# Add title and improve layout
ax.set_title('Sensor Process Analysis - Optimized Flow Visualization v6',
            fontsize=16, fontweight='bold', color='#2C3E50', pad=20)

# Set axis limits for better spacing
ax.set_xlim(-1.5, 3.5)
ax.set_ylim(-1.5, 2.5)
ax.set_aspect('equal')
ax.axis('off')
plt.tight_layout()

# Save the optimized diagram
out_path = 'sensor_process_analysis_v6_optimized.png'
fig.savefig(out_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Optimized Sensor Process Analysis v6 saved as '{out_path}'")

# Also save as PDF and SVG
pdf_path = 'sensor_process_analysis_v6_optimized.pdf'
fig.savefig(pdf_path, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Optimized PDF version saved as '{pdf_path}'")

svg_path = 'sensor_process_analysis_v6_optimized.svg'
fig.savefig(svg_path, bbox_inches='tight', facecolor='white', edgecolor='none')
print(f"Optimized SVG version saved as '{svg_path}'")

plt.show()
