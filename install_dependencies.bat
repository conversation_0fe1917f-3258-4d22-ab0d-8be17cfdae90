@echo off
echo Installing Python dependencies for Process Discovery Network Diagram...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Upgrade pip first
python -m pip install --upgrade pip

REM Install core dependencies
echo Installing core dependencies...
python -m pip install networkx>=3.1
python -m pip install matplotlib>=3.7.0
python -m pip install numpy>=1.24.0
python -m pip install pandas>=2.0.0

REM Install visualization dependencies
echo Installing visualization dependencies...
python -m pip install Pillow>=10.0.0
python -m pip install seaborn>=0.12.0

REM Install optional dependencies (with error handling)
echo Installing optional dependencies...
python -m pip install plotly>=5.15.0
python -m pip install scipy>=1.11.0

echo.
echo Installation complete!
echo.
echo To test the installation, run:
echo python network_diagram.py
echo.
pause
