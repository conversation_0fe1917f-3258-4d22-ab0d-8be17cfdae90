import networkx as nx
import matplotlib.pyplot as plt
import math

def fmt(num):
    """Format numbers with thousands separator for readability (MDPI rule)"""
    if num is None:
        return ""
    return f"{num:,}" if abs(num) >= 10000 else str(num)

def edge_width(weight):
    """Calculate edge width based on flow volume"""
    if weight is None:
        return 2.5
    return 3.5 if weight >= 1000 else 2.5

# Optimized node positions for clean layout
pos = {
    'Start': (-1, 2),
    'Station 202': (0, 2),
    'Station 203': (1, 2),
    'Station 204': (2, 2),
    'Station 205': (0, 1),
    'Station 206': (1, 1),
    'Station 207': (2, 1),
    'Station 208': (0, 0),
    'Station 209': (1, 0),
    'End': (3, -1)
}

G = nx.MultiDiGraph()
for n in pos: 
    G.add_node(n)

# Edge definitions with optimized curves and labeling
edges = [
    # Green flows (normal process paths)
    ('Start', 'Station 202', 4755, 'green', -0.2, True),
    ('Start', 'Station 203', 5592, 'green', 0.2, True),
    ('Start', 'Station 204', 4474, 'green', 0.35, True),
    ('Station 206', 'Station 207', 8276, 'green', 0.0, True),
    ('Station 208', 'Station 207', 7596, 'green', 0.05, True),
    ('Station 208', 'Station 209', 2529, 'green', 0.0, True),
    ('Station 208', 'End', 6028, 'green', 0.4, True),
    ('Station 209', 'End', 1476, 'green', -0.25, True),
    ('Station 207', 'End', 6379, 'green', 0.0, True),

    # Red flows without labels
    ('Station 208', 'Station 205', None, 'red', -0.15, False),
    ('Station 205', 'Station 202', None, 'red', 0.15, False),
    ('Station 202', 'Station 209', None, 'red', 0.25, False),
    ('Station 202', 'Station 207', None, 'red', 0.3, False),
    ('Station 206', 'Station 203', None, 'red', 0.25, False),
    ('Station 209', 'Station 206', None, 'red', -0.25, False),

    # Red flows with labels
    ('Station 205', 'Station 204', 8485, 'red', -0.15, True),
    ('Station 205', 'Station 203', 529, 'red', 0.15, True),
    ('Station 202', 'Station 203', 8947, 'red', -0.1, True),
    ('Station 203', 'Station 204', 9200, 'red', 0.0, True),

    # Additional green flows
    ('Station 202', 'Station 206', 529, 'green', 0.0, True),
    ('Station 205', 'Station 206', 2888, 'green', 0.0, True),
    ('Station 207', 'Station 209', 576, 'green', 0.0, True),
]

# Add edges to graph
for u, v, w, c, rad, label in edges:
    G.add_edge(u, v, weight=w, color=c, rad=rad, label=label)

# Create the diagram
fig, ax = plt.subplots(figsize=(14, 8), dpi=300)

# Draw edges with optimized styling
for u, v, k, data in G.edges(keys=True, data=True):
    w = data['weight']
    color = data['color']
    rad = data['rad']
    lbl = data['label']
    
    # Draw edges with clear arrowheads
    nx.draw_networkx_edges(
        G, pos, edgelist=[(u, v)], edge_color=color, arrowstyle='-|>', arrows=True,
        arrowsize=22, connectionstyle=f'arc3,rad={rad}', width=edge_width(w),
        ax=ax, min_source_margin=4, min_target_margin=4)

    # Add labels with smart positioning
    if lbl and w is not None:
        xm = (pos[u][0] + pos[v][0]) / 2
        ym = (pos[u][1] + pos[v][1]) / 2
        dx = pos[v][0] - pos[u][0]
        dy = pos[v][1] - pos[u][1]
        length = math.hypot(dx, dy) or 1e-5
        px, py = -dy / length, dx / length
        offset = 0.14 + abs(rad) * 0.15
        sign = 1 if rad >= 0 else -1
        xlab = xm + px * offset * sign
        ylab = ym + py * offset * sign
        ax.text(xlab, ylab, fmt(w), fontsize=9, ha='center', va='center')

# Draw nodes with proper sizing
for n, (x, y) in pos.items():
    if n in ('Start', 'End'):
        r = 0.26
        circ = plt.Circle((x, y), r, ec='black', fc='lightblue', lw=1.6)
        ax.add_patch(circ)
        ax.text(x, y, 'Process\n' + ('Start' if n == 'Start' else 'End'), 
               fontsize=12, ha='center', va='center')
    else:
        bottleneck = n in ('Station 202', 'Station 203', 'Station 204', 'Station 205')
        ec = 'red' if bottleneck else 'blue'
        events = {
            'Station 202': 47559, 'Station 203': 55921, 'Station 204': 44746, 'Station 205': 52673,
            'Station 206': 51387, 'Station 207': 63798, 'Station 208': 60281, 'Station 209': 14762
        }[n]
        bbox = dict(boxstyle="round,pad=1.0", fc='white', ec=ec, lw=2)
        ax.text(x, y, f"{n}\n{fmt(events)} events", fontsize=11, ha='center', va='center', bbox=bbox)

ax.axis('off')
plt.tight_layout()

# Save the optimized diagram
out_path = 'sensor_process_analysis_v13_final.png'
fig.savefig(out_path, dpi=300, bbox_inches='tight')
print(f"Final optimized diagram saved as '{out_path}'")

# Also save as PDF and SVG
pdf_path = 'sensor_process_analysis_v13_final.pdf'
fig.savefig(pdf_path, bbox_inches='tight')
print(f"PDF version saved as '{pdf_path}'")

svg_path = 'sensor_process_analysis_v13_final.svg'
fig.savefig(svg_path, bbox_inches='tight')
print(f"SVG version saved as '{svg_path}'")

plt.show()
