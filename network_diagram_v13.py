import networkx as nx
import matplotlib.pyplot as plt
import math

def fmt(num):
    """Format numbers with thousands separator for readability (MDPI rule)"""
    if num is None:
        return ""
    # Use comma separator for numbers with 5 or more digits (≥10,000)
    return f"{num:,}" if abs(num) >= 10000 else str(num)

def edge_width(weight):
    """Calculate edge width based on flow volume for high-resolution display"""
    if weight is None:
        return 3.0  # Thicker for better visibility
    return 4.5 if weight >= 1000 else 3.0  # Increased thickness for legibility

# Optimized horizontal layout for Overleaf - expanded for better visibility
pos = {
    'Start': (-2, 2),
    'Station 202': (0, 2),
    'Station 203': (2, 2),
    'Station 204': (4, 2),
    'Station 205': (0, 1),
    'Station 206': (2, 1),
    'Station 207': (4, 1),
    'Station 208': (0, 0),
    'Station 209': (2, 0),
    'End': (6, 0)
}

G = nx.MultiDiGraph()
for n in pos: 
    G.add_node(n)

# Edge definitions with optimized curves and labeling
edges = [
    # Green flows (normal process paths)
    ('Start', 'Station 202', 4755, 'green', -0.2, True),
    ('Start', 'Station 203', 5592, 'green', 0.2, True),
    ('Start', 'Station 204', 4474, 'green', 0.35, True),
    ('Station 206', 'Station 207', 8276, 'green', 0.0, True),
    ('Station 208', 'Station 207', 7596, 'green', 0.05, True),
    ('Station 208', 'Station 209', 2529, 'green', 0.0, True),
    ('Station 208', 'End', 6028, 'green', 0.4, True),
    ('Station 209', 'End', 1476, 'green', -0.25, True),
    ('Station 207', 'End', 6379, 'green', 0.0, True),

    # Red flows without labels
    ('Station 208', 'Station 205', None, 'red', -0.15, False),
    ('Station 205', 'Station 202', None, 'red', 0.15, False),
    ('Station 202', 'Station 209', None, 'red', 0.25, False),
    ('Station 202', 'Station 207', None, 'red', 0.3, False),
    ('Station 206', 'Station 203', None, 'red', 0.25, False),
    ('Station 209', 'Station 206', None, 'red', -0.25, False),

    # Red flows with labels
    ('Station 205', 'Station 204', 8485, 'red', -0.15, True),
    ('Station 205', 'Station 203', 529, 'red', 0.15, True),
    ('Station 202', 'Station 203', 8947, 'red', -0.1, True),
    ('Station 203', 'Station 204', 9200, 'red', 0.0, True),

    # Additional green flows
    ('Station 202', 'Station 206', 529, 'green', 0.0, True),
    ('Station 205', 'Station 206', 2888, 'green', 0.0, True),
    ('Station 207', 'Station 209', 576, 'green', 0.0, True),
]

# Add edges to graph
for u, v, w, c, rad, label in edges:
    G.add_edge(u, v, weight=w, color=c, rad=rad, label=label)

# Create optimized horizontal layout for Overleaf
fig, ax = plt.subplots(figsize=(18, 8), dpi=400)  # Wide horizontal format for Overleaf

# Draw edges with optimized styling
for u, v, k, data in G.edges(keys=True, data=True):
    w = data['weight']
    color = data['color']
    rad = data['rad']
    lbl = data['label']
    
    # Draw edges with larger, clearer arrowheads for high resolution
    nx.draw_networkx_edges(
        G, pos, edgelist=[(u, v)], edge_color=color, arrowstyle='-|>', arrows=True,
        arrowsize=30, connectionstyle=f'arc3,rad={rad}', width=edge_width(w),
        ax=ax, min_source_margin=6, min_target_margin=6)

    # Add labels positioned exactly on the curve path
    if lbl and w is not None:
        # Calculate the midpoint of the edge
        xm = (pos[u][0] + pos[v][0]) / 2
        ym = (pos[u][1] + pos[v][1]) / 2

        # For curved edges, calculate exact position on the curve
        if abs(rad) > 0.01:  # If there's a significant curve
            dx = pos[v][0] - pos[u][0]
            dy = pos[v][1] - pos[u][1]
            length = math.hypot(dx, dy) or 1e-5

            # Calculate the center of the arc
            mid_x = (pos[u][0] + pos[v][0]) / 2
            mid_y = (pos[u][1] + pos[v][1]) / 2

            # Perpendicular vector for curve calculation
            px, py = -dy / length, dx / length

            # Calculate the exact curve position using arc geometry
            # This places the label exactly on the curved path
            curve_height = rad * length * 0.5  # Height of the curve
            xlab = mid_x + px * curve_height
            ylab = mid_y + py * curve_height
        else:
            # For straight lines, place directly on the line
            xlab = xm
            ylab = ym

        # Enhanced label styling optimized for Overleaf
        ax.text(xlab, ylab, fmt(w), fontsize=12, ha='center', va='center',
               fontweight='bold', color='black',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white',
               edgecolor=color, linewidth=2.5, alpha=0.98))

# Draw nodes with optimized sizing for Overleaf
for n, (x, y) in pos.items():
    if n in ('Start', 'End'):
        # Perfect circles for start/end with professional styling
        r = 0.4  # Optimal radius for perfect circles
        circ = plt.Circle((x, y), r, ec='#2C3E50', fc='#3498DB', lw=3)
        ax.add_patch(circ)
        # Enhanced text styling for better visibility
        ax.text(x, y, 'Process\n' + ('Start' if n == 'Start' else 'End'),
               fontsize=13, ha='center', va='center', fontweight='bold', color='white')
    else:
        # Enhanced station styling for Overleaf
        bottleneck = n in ('Station 202', 'Station 203', 'Station 204', 'Station 205')
        ec = '#E74C3C' if bottleneck else '#3498DB'  # Professional colors
        fc = '#FADBD8' if bottleneck else '#EBF5FB'  # Light backgrounds
        events = {
            'Station 202': 47559, 'Station 203': 55921, 'Station 204': 44746, 'Station 205': 52673,
            'Station 206': 51387, 'Station 207': 63798, 'Station 208': 60281, 'Station 209': 14762
        }[n]
        bbox = dict(boxstyle="round,pad=1.3", fc=fc, ec=ec, lw=3)
        text_color = '#C0392B' if bottleneck else '#2E86AB'
        ax.text(x, y, f"{n}\n{fmt(events)} events", fontsize=12, ha='center', va='center',
               bbox=bbox, fontweight='bold', color=text_color)

# Optimize layout for Overleaf with proper spacing
ax.set_xlim(-2.8, 6.8)  # Horizontal expansion for better visibility
ax.set_ylim(-0.8, 2.8)  # Proper vertical spacing
ax.set_aspect('equal')
ax.axis('off')

# Add subtle title for context
ax.text(2, 2.6, 'Sensor Process Flow Analysis', fontsize=16, ha='center', va='center',
        fontweight='bold', color='#2C3E50')

plt.tight_layout(pad=0.5)

# Save the optimized diagram for Overleaf
out_path = 'sensor_process_analysis_v13_overleaf_optimized.png'
fig.savefig(out_path, dpi=400, bbox_inches='tight', facecolor='white',
           edgecolor='none', pad_inches=0.3)
print(f"Overleaf-optimized diagram saved as '{out_path}' (400 DPI)")

# Also save as PDF and SVG with high quality
pdf_path = 'sensor_process_analysis_v13_overleaf_optimized.pdf'
fig.savefig(pdf_path, bbox_inches='tight', facecolor='white',
           edgecolor='none', pad_inches=0.3)
print(f"Overleaf-optimized PDF saved as '{pdf_path}'")

svg_path = 'sensor_process_analysis_v13_overleaf_optimized.svg'
fig.savefig(svg_path, bbox_inches='tight', facecolor='white',
           edgecolor='none', pad_inches=0.3)
print(f"Overleaf-optimized SVG saved as '{svg_path}'")

plt.show()
